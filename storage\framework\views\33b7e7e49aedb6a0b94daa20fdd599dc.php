<!-- Shared Post Comment Modal -->
<div id="shareCommentModal-<?php echo e($share->id); ?>"
     class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 hidden"
     onclick="closeSharedCommentModal(<?php echo e($share->id); ?>, event)">
    
    <div class="relative w-full max-w-4xl bg-white rounded-lg shadow-2xl flex flex-col overflow-hidden"
         style="height: 90vh; max-height: 90vh;"
         onclick="event.stopPropagation()">
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-200 flex-shrink-0">
            <h3 class="text-lg font-medium text-gray-900">
                <?php echo e($share->user->name); ?>'s shared post
            </h3>
            <button onclick="closeSharedCommentModal(<?php echo e($share->id); ?>)" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>

        <!-- Shared Post Content -->
        <div class="p-4 border-b border-gray-200 flex-shrink-0">
            <!-- Share Header -->
            <div class="flex items-center space-x-3 mb-3">
                <a href="<?php echo e(route('profile.user', $share->user)); ?>">
                    <img class="h-10 w-10 rounded-full" 
                         src="<?php echo e($share->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                         alt="<?php echo e($share->user->name); ?>">
                </a>
                <div>
                    <div class="flex items-center space-x-2">
                        <a href="<?php echo e(route('profile.user', $share->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green">
                            <?php echo e($share->user->name); ?>

                        </a>
                        <span class="text-gray-500">shared a post</span>
                    </div>
                    <p class="text-sm text-gray-500"><?php echo e($share->created_at->diffForHumans()); ?></p>
                </div>
            </div>

            <!-- Share Message -->
            <!--[if BLOCK]><![endif]--><?php if($share->message): ?>
                <div class="mb-4">
                    <p class="text-gray-800"><?php echo nl2br(e($share->message)); ?></p>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Original Post Preview -->
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div class="flex items-center space-x-3 mb-3">
                    <a href="<?php echo e(route('profile.user', $share->post->user)); ?>">
                        <img class="h-8 w-8 rounded-full" 
                             src="<?php echo e($share->post->user->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url($share->post->user->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($share->post->user->name) . '&color=7BC74D&background=EEEEEE'); ?>" 
                             alt="<?php echo e($share->post->user->name); ?>">
                    </a>
                    <div>
                        <a href="<?php echo e(route('profile.user', $share->post->user)); ?>" class="font-semibold text-gray-900 hover:text-custom-green text-sm">
                            <?php echo e($share->post->user->name); ?>

                        </a>
                        <p class="text-xs text-gray-500"><?php echo e($share->post->created_at->diffForHumans()); ?></p>
                    </div>
                </div>
                <div class="text-gray-800 text-sm">
                    <?php echo nl2br(e($share->post->content)); ?>

                </div>
            </div>
        </div>

        <!-- Reaction Summary Bar -->
        <div class="px-4 py-2 border-b border-gray-200 flex-shrink-0">
            <div class="flex items-center justify-between text-sm text-gray-600">
                <div class="flex items-center space-x-2">
                    <!--[if BLOCK]><![endif]--><?php if($share->reactions()->count() > 0): ?>
                        <div class="flex items-center space-x-1">
                            <?php
                                $reactionCounts = $share->reactions()
                                    ->selectRaw('type, COUNT(*) as count')
                                    ->groupBy('type')
                                    ->pluck('count', 'type')
                                    ->toArray();
                                $totalReactions = array_sum($reactionCounts);
                            ?>
                            
                            <?php $__currentLoopData = ['like', 'love', 'haha', 'wow', 'sad', 'angry']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <!--[if BLOCK]><![endif]--><?php if(isset($reactionCounts[$type]) && $reactionCounts[$type] > 0): ?>
                                    <?php $details = \App\Models\Reaction::getReactionDetails($type); ?>
                                    <img src="<?php echo e($details['emoji']); ?>" alt="<?php echo e($details['label']); ?>" class="w-4 h-4">
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            
                            <span><?php echo e($totalReactions); ?></span>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
                
                <div class="flex items-center space-x-4">
                    <!--[if BLOCK]><![endif]--><?php if($share->comments()->count() > 0): ?>
                        <span><?php echo e($share->comments()->count()); ?> comment<?php echo e($share->comments()->count() !== 1 ? 's' : ''); ?></span>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="px-4 py-3 border-b border-gray-200 flex-shrink-0">
            <div class="flex items-center justify-around">
                <!-- Reaction Button -->
                <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-post-reactions', ['share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'modal-reactions-'.$share->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>

                <button class="flex items-center space-x-2 text-gray-600 hover:text-blue-500 transition-colors py-2 px-4 rounded-lg hover:bg-gray-100">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                    <span class="font-medium">Comment</span>
                </button>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="flex-1 overflow-y-auto">
            <!-- Sort Options -->
            <!--[if BLOCK]><![endif]--><?php if(count($comments) > 1): ?>
                <div class="p-4 border-b border-gray-100">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Sort by:</span>
                        <button wire:click="updateSort('newest')" 
                                class="text-sm <?php echo e($sortBy === 'newest' ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600'); ?>">
                            Newest first
                        </button>
                        <button wire:click="updateSort('oldest')" 
                                class="text-sm <?php echo e($sortBy === 'oldest' ? 'text-blue-600 font-medium' : 'text-gray-600 hover:text-blue-600'); ?>">
                            Oldest first
                        </button>
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

            <!-- Comments List -->
            <div class="divide-y divide-gray-100">
                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $comments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('shared-comment-item', ['comment' => $comment,'share' => $share]);

$__html = app('livewire')->mount($__name, $__params, 'comment-'.$comment->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="p-8 text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <p class="text-lg font-medium mb-2">No comments yet</p>
                        <p>Be the first to comment on this shared post!</p>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>

        <!-- Add Comment Form -->
        <!--[if BLOCK]><![endif]--><?php if(auth()->guard()->check()): ?>
        <div class="flex-shrink-0 p-4 border-t border-gray-200 bg-white">
            <form wire:submit.prevent="addComment" class="flex space-x-3">
                <div class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full"
                         src="<?php echo e(auth()->user()->avatar ? \Illuminate\Support\Facades\Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE'); ?>"
                         alt="<?php echo e(auth()->user()->name); ?>">
                </div>
                <div class="flex-1 min-w-0">
                    <div class="relative">
                        <textarea wire:model="newComment" 
                                  rows="1"
                                  placeholder="Write a comment..."
                                  class="block w-full resize-none border-0 bg-gray-100 rounded-full py-3 px-4 text-sm placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:bg-white transition-colors"
                                  style="min-height: 44px; max-height: 120px;"
                                  x-data="{ resize: () => { $el.style.height = 'auto'; $el.style.height = Math.min($el.scrollHeight, 120) + 'px'; } }"
                                  x-init="resize()"
                                  @input="resize()"
                                  @keydown.enter.prevent="if (!$event.shiftKey && $wire.newComment.trim()) { $wire.addComment(); }"></textarea>
                        
                        <!--[if BLOCK]><![endif]--><?php if($newComment): ?>
                            <button type="submit" 
                                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-700 p-1">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                                </svg>
                            </button>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </form>
        </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-comment-modal.blade.php ENDPATH**/ ?>