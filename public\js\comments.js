// Comment functionality
document.addEventListener('DOMContentLoaded', function() {
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        // Close regular comment dropdowns
        if (!e.target.closest('[id^="comment-dropdown-"]') && !e.target.closest('button[onclick*="toggleCommentDropdown"]')) {
            document.querySelectorAll('[id^="comment-dropdown-"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }

        // Close share comment dropdowns
        if (!e.target.closest('[id^="share-comment-dropdown-"]') && !e.target.closest('button[onclick*="toggleShareCommentDropdown"]')) {
            document.querySelectorAll('[id^="share-comment-dropdown-"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });

    // Handle comment form submissions
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('comment-form')) {
            e.preventDefault();
            submitComment(e.target);
        }
        
        if (e.target.classList.contains('internal-share-form')) {
            e.preventDefault();
            submitInternalShare(e.target);
        }
        
        if (e.target.classList.contains('edit-comment-form')) {
            e.preventDefault();
            submitCommentEdit(e.target);
        }

        if (e.target.classList.contains('edit-share-form')) {
            e.preventDefault();
            submitShareEdit(e.target);
        }

        if (e.target.classList.contains('share-comment-form')) {
            e.preventDefault();
            submitShareComment(e.target);
        }

        if (e.target.classList.contains('share-comment-edit-form')) {
            e.preventDefault();
            submitShareCommentEdit(e.target);
        }

        if (e.target.classList.contains('share-comment-reply-form')) {
            e.preventDefault();
            submitShareCommentReply(e.target);
        }
    });
});

// Submit new comment
async function submitComment(form) {
    const formData = new FormData(form);
    const postId = form.dataset.postId;
    const parentId = form.dataset.parentId;

    if (parentId) {
        formData.append('parent_id', parentId);
    }

    try {
        const response = await fetch(`/posts/${postId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Clear the form
            form.reset();

            // Hide reply form if it was a reply
            if (parentId) {
                hideReplyForm(parentId);
                // Add reply to the parent comment's replies section
                addReplyToDOM(parentId, data.comment);
            } else {
                // Add new main comment to the comments list
                addCommentToDOM(postId, data.comment);
            }

            // Update comment count
            updateCommentCount(postId);

            // Trigger real-time summary update
            if (window.postSummaryUpdater) {
                window.postSummaryUpdater.onCommentChange(postId);
            }
        } else {
            alert('Error posting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting comment:', error);
        alert('Error posting comment. Please try again.');
    }
}

// Submit internal share
async function submitInternalShare(form) {
    const formData = new FormData(form);
    const postId = form.dataset.postId;
    
    try {
        const response = await fetch(`/posts/${postId}/share/timeline`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            form.reset();
            closeShareModal(postId);
            updateShareCount(postId, data.shares_count);

            // Trigger real-time summary update
            if (window.postSummaryUpdater) {
                window.postSummaryUpdater.onShareChange(postId);
            }

            // Show success message and refresh page to show shared post
            alert('Post shared successfully! The page will refresh to show your shared post.');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            alert('Error sharing post: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error sharing post:', error);
        alert('Error sharing post. Please try again.');
    }
}

// Toggle comment like
async function toggleCommentLike(commentId) {
    try {
        const response = await fetch(`/comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const likeBtn = document.getElementById(`comment-like-btn-${commentId}`);
            const likeCount = document.getElementById(`comment-like-count-${commentId}`);
            const heartIcon = likeBtn.querySelector('svg');

            // Add animation effect
            likeBtn.style.transform = 'scale(1.1)';
            setTimeout(() => {
                likeBtn.style.transform = 'scale(1)';
            }, 150);

            if (data.liked) {
                heartIcon.classList.add('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'currentColor');
                // Add a subtle bounce animation
                heartIcon.style.animation = 'heartBeat 0.6s ease-in-out';
                setTimeout(() => {
                    heartIcon.style.animation = '';
                }, 600);
            } else {
                heartIcon.classList.remove('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'none');
            }

            likeCount.textContent = data.likes_count;
        }
    } catch (error) {
        console.error('Error toggling comment like:', error);
    }
}

// Show reply form
function showReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.remove('hidden');
        replyForm.querySelector('textarea').focus();
    }
}

// Hide reply form
function hideReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.add('hidden');
        replyForm.querySelector('form').reset();
    }
}

// Edit comment
function editComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .comment-edit-form`);
    
    if (commentContent && editForm) {
        commentContent.classList.add('hidden');
        editForm.classList.remove('hidden');
        editForm.querySelector('textarea').focus();
    }
}

// Cancel edit comment
function cancelEditComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .comment-edit-form`);
    
    if (commentContent && editForm) {
        commentContent.classList.remove('hidden');
        editForm.classList.add('hidden');
    }
}

// Submit comment edit
async function submitCommentEdit(form) {
    const commentId = form.dataset.commentId;
    const formData = new FormData(form);
    formData.append('_method', 'PUT');

    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Update comment content
            const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content p`);
            if (commentContent) {
                commentContent.innerHTML = data.comment.content.replace(/\n/g, '<br>');
            }
            
            cancelEditComment(commentId);
        } else {
            alert('Error updating comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error updating comment:', error);
        alert('Error updating comment. Please try again.');
    }
}

// Delete comment
async function deleteComment(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) {
        return;
    }
    
    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Remove comment from DOM
            const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
            let postId = null;

            if (commentElement) {
                // Try to extract postId from the comment element or its parent
                const postCard = commentElement.closest('[data-post-id]');
                if (postCard) {
                    postId = postCard.dataset.postId;
                }
                commentElement.remove();
            }

            // Trigger real-time summary update if we have the postId
            if (postId && window.postSummaryUpdater) {
                window.postSummaryUpdater.onCommentChange(postId);
            }
        } else {
            alert('Error deleting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting comment:', error);
        alert('Error deleting comment. Please try again.');
    }
}

// Add new comment to DOM
function addCommentToDOM(postId, comment) {
    const commentsList = document.getElementById(`comments-list-${postId}`);
    if (!commentsList) return;

    // Remove "no comments" message if it exists
    const noComments = commentsList.querySelector('.no-comments');
    if (noComments) {
        noComments.remove();
    }

    // Create comment HTML
    const commentHTML = createCommentHTML(comment, postId);

    // Add to the top of comments list
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);

    // Initialize reactions for the new comment
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }
}

// Add new reply to DOM
function addReplyToDOM(parentId, reply) {
    const parentComment = document.querySelector(`[data-comment-id="${parentId}"]`);
    if (!parentComment) return;

    // Find or create the nested-comments section
    let nestedComments = parentComment.querySelector('.nested-comments');
    if (!nestedComments) {
        // Create nested comments section if it doesn't exist
        const repliesContainer = document.createElement('div');
        repliesContainer.className = 'nested-comments mt-4 ml-4 space-y-3 border-l-2 border-gray-100 pl-4';
        parentComment.querySelector('.flex-1').appendChild(repliesContainer);
        nestedComments = repliesContainer;
    }

    // Create reply HTML
    const replyHTML = createCommentHTML(reply, reply.commentable_id);

    // Add to the bottom of replies
    nestedComments.insertAdjacentHTML('beforeend', replyHTML);

    // Initialize reactions for the new reply
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }
}

// Create comment HTML structure
function createCommentHTML(comment, postId) {
    const timeAgo = formatTimeAgo(comment.created_at);
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                            <div class="relative ml-auto" x-data="{ open: false }">
                                <button onclick="toggleCommentDropdown(${comment.id})" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                                <div id="comment-dropdown-${comment.id}" class="hidden absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                    <button onclick="editComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>Edit</span>
                                    </button>
                                    <button onclick="deleteComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        <span>Delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="comment-content">
                            <p class="text-gray-700 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>

                        <!-- Edit form (hidden by default) -->
                        <div class="comment-edit-form hidden mt-3">
                            <form class="edit-comment-form" data-comment-id="${comment.id}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <input type="hidden" name="_method" value="PUT">
                                <textarea name="content" rows="2"
                                          class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">${comment.content}</textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="cancelEditComment(${comment.id})"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                        Save
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        ${FacebookReactionSystem.generateReactionHTML(comment.id, 'comment', comment.user_reaction, comment.reaction_counts, true)}

                        <button onclick="showReplyForm(${comment.id})" class="flex items-center space-x-1 text-gray-500 hover:text-blue-600 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>
                    </div>

                    <!-- Reply Form (hidden by default) -->
                    <div class="reply-form hidden mt-3 ml-4" id="reply-form-${comment.id}">
                        <form class="comment-form" data-post-id="${postId}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                         src="${getCurrentUserAvatar()}"
                                         alt="Your avatar">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="relative">
                                        <textarea name="content" rows="1"
                                                  placeholder="Write a reply..."
                                                  class="w-full px-3 py-2 border border-gray-200 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200"
                                                  required></textarea>
                                    </div>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="hideReplyForm(${comment.id})"
                                                class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                        <button type="submit"
                                                class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                            Reply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Helper function to format time ago
function formatTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
}

// Helper function to get current user avatar
function getCurrentUserAvatar() {
    const userAvatarElement = document.querySelector('.comment-form img');
    return userAvatarElement ? userAvatarElement.src : 'https://ui-avatars.com/api/?name=User&color=7BC74D&background=EEEEEE';
}

// Toggle comment dropdown menu
function toggleCommentDropdown(commentId) {
    const dropdown = document.getElementById(`comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.toggle('hidden');

        // Close other dropdowns
        document.querySelectorAll('[id^="comment-dropdown-"]').forEach(otherDropdown => {
            if (otherDropdown.id !== `comment-dropdown-${commentId}`) {
                otherDropdown.classList.add('hidden');
            }
        });
    }
}

// Hide comment dropdown menu
function hideCommentDropdown(commentId) {
    const dropdown = document.getElementById(`comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// Toggle share comment dropdown menu
function toggleShareCommentDropdown(commentId) {
    const dropdown = document.getElementById(`share-comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.toggle('hidden');

        // Close other share comment dropdowns
        document.querySelectorAll('[id^="share-comment-dropdown-"]').forEach(otherDropdown => {
            if (otherDropdown.id !== `share-comment-dropdown-${commentId}`) {
                otherDropdown.classList.add('hidden');
            }
        });
    }
}

// Hide share comment dropdown menu
function hideShareCommentDropdown(commentId) {
    const dropdown = document.getElementById(`share-comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.closest('[id^="comment-dropdown-"]') && !event.target.closest('button[onclick*="toggleCommentDropdown"]')) {
        document.querySelectorAll('[id^="comment-dropdown-"]').forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
});

// Helper function to get current user ID
function getCurrentUserId() {
    // Try to get user ID from a data attribute or meta tag
    const userIdMeta = document.querySelector('meta[name="user-id"]');
    if (userIdMeta) {
        return userIdMeta.getAttribute('content');
    }

    // Fallback: try to get from any existing comment edit button
    const editButton = document.querySelector('[onclick*="editComment"]');
    if (editButton) {
        // This is a fallback - in practice, we should have the user ID in a meta tag
        return 'current-user'; // This will work for newly created comments
    }

    return null;
}

// Update comment count
function updateCommentCount(postId) {
    // This is a simplified version - in a real app you'd fetch the actual count
    const countElement = document.getElementById(`comments-count-${postId}`);
    if (countElement) {
        const currentCount = parseInt(countElement.textContent.match(/\d+/)[0]);
        countElement.textContent = `${currentCount + 1} comments`;
    }
}

// Update share count
function updateShareCount(postId, newCount) {
    const countElement = document.getElementById(`shares-count-${postId}`);
    if (countElement) {
        countElement.textContent = `${newCount} shares`;
    }
}

// Edit share message
function editShareMessage(shareId) {
    const messageElement = document.getElementById(`share-message-${shareId}`);
    const editForm = document.getElementById(`edit-share-form-${shareId}`);

    if (messageElement) {
        messageElement.classList.add('hidden');
    }
    if (editForm) {
        editForm.classList.remove('hidden');
        editForm.querySelector('textarea').focus();
    }
}

// Cancel edit share
function cancelEditShare(shareId) {
    const messageElement = document.getElementById(`share-message-${shareId}`);
    const editForm = document.getElementById(`edit-share-form-${shareId}`);

    if (messageElement) {
        messageElement.classList.remove('hidden');
    }
    if (editForm) {
        editForm.classList.add('hidden');
    }
}

// Submit share edit
async function submitShareEdit(form) {
    const shareId = form.dataset.shareId;
    const formData = new FormData(form);
    formData.append('_method', 'PUT');

    try {
        const response = await fetch(`/shares/${shareId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update the message display
            const messageElement = document.getElementById(`share-message-${shareId}`);
            if (messageElement) {
                const newMessage = data.share.message;
                if (newMessage) {
                    messageElement.querySelector('p').innerHTML = newMessage.replace(/\n/g, '<br>');
                    messageElement.classList.remove('hidden');
                } else {
                    messageElement.classList.add('hidden');
                }
            }

            // Hide the edit form
            cancelEditShare(shareId);

            // Update privacy scope display if it exists
            const privacyScopeElement = document.querySelector(`[data-share-id="${shareId}"] .privacy-scope`);
            if (privacyScopeElement && data.share.privacy_scope) {
                const privacyText = data.share.privacy_scope.charAt(0).toUpperCase() + data.share.privacy_scope.slice(1).replace('_', ' ');
                privacyScopeElement.textContent = privacyText;
            }
        } else {
            alert('Error updating share: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error updating share:', error);
        alert('Error updating share. Please try again.');
    }
}

// ===== SHARE ENGAGEMENT FUNCTIONS =====

// Toggle like on share
async function toggleShareLike(shareId) {
    try {
        const response = await fetch(`/shares/${shareId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            const button = document.getElementById(`share-like-btn-${shareId}`);
            const countElement = document.getElementById(`share-like-count-${shareId}`);
            const heartIcon = button.querySelector('svg');

            if (data.liked) {
                heartIcon.classList.add('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'currentColor');
            } else {
                heartIcon.classList.remove('text-red-600', 'fill-current');
                heartIcon.setAttribute('fill', 'none');
            }

            countElement.textContent = `${data.likes_count} likes`;
        }
    } catch (error) {
        console.error('Error toggling share like:', error);
    }
}

// Toggle comments section for share
function toggleShareComments(shareId) {
    const commentsSection = document.getElementById(`share-comments-section-${shareId}`);
    if (commentsSection) {
        commentsSection.classList.toggle('hidden');
    }
}

// Submit share comment
async function submitShareComment(form) {
    const shareId = form.dataset.shareId;
    const formData = new FormData(form);

    const csrfToken = document.querySelector('meta[name="csrf-token"]');

    try {
        const response = await fetch(`/shares/${shareId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken ? csrfToken.getAttribute('content') : ''
            }
        });

        const data = await response.json();

        if (data.success) {
            // Clear the form
            form.reset();

            // Update comments count
            const countElement = document.getElementById(`share-comments-count-${shareId}`);
            if (countElement) {
                countElement.textContent = `${data.comments_count} comments`;
            }

            // Add new comment to DOM
            addShareCommentToDOM(shareId, data.comment);
        } else {
            alert('Error adding comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting share comment:', error);
        alert('Error adding comment. Please try again.');
    }
}

// Toggle like on share comment
async function toggleShareCommentLike(commentId) {
    try {
        const response = await fetch(`/share-comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Find the like button by its specific ID
            const likeButton = document.getElementById(`share-comment-like-btn-${commentId}`);
            const likeCount = document.getElementById(`share-comment-like-count-${commentId}`);

            if (likeButton) {
                const heartIcon = likeButton.querySelector('svg');

                if (data.liked) {
                    likeButton.classList.add('text-red-600');
                    likeButton.classList.remove('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.add('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'currentColor');
                    }
                } else {
                    likeButton.classList.remove('text-red-600');
                    likeButton.classList.add('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.remove('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'none');
                    }
                }
            }

            if (likeCount) {
                likeCount.textContent = data.likes_count || 0;
            }
        }
    } catch (error) {
        console.error('Error toggling share comment like:', error);
    }
}

// View original post
function viewOriginalPost(postId) {
    window.location.href = `/posts/${postId}`;
}

// ===== SHARE COMMENT MANAGEMENT FUNCTIONS =====

// Edit share comment
function editShareComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .comment-edit-form`);

    if (commentContent && editForm) {
        commentContent.classList.add('hidden');
        editForm.classList.remove('hidden');
        editForm.querySelector('textarea').focus();
    }
}

// Cancel edit share comment
function cancelEditShareComment(commentId) {
    const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content`);
    const editForm = document.querySelector(`[data-comment-id="${commentId}"] .comment-edit-form`);

    if (commentContent && editForm) {
        commentContent.classList.remove('hidden');
        editForm.classList.add('hidden');
    }
}

// Submit share comment edit
async function submitShareCommentEdit(form) {
    const commentId = form.dataset.commentId;
    const formData = new FormData(form);
    formData.append('_method', 'PUT');

    try {
        const response = await fetch(`/share-comments/${commentId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update comment content
            const commentContent = document.querySelector(`[data-comment-id="${commentId}"] .comment-content p`);
            if (commentContent) {
                commentContent.innerHTML = data.comment.content.replace(/\n/g, '<br>');
            }

            cancelEditShareComment(commentId);
        } else {
            alert('Error updating comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error updating share comment:', error);
        alert('Error updating comment. Please try again.');
    }
}

// Delete share comment
async function deleteShareComment(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) {
        return;
    }

    try {
        const response = await fetch(`/share-comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove the comment from DOM
            const commentElement = document.querySelector(`[data-comment-id="${commentId}"]`);
            if (commentElement) {
                commentElement.remove();
            }

            // Update comment count if available
            if (data.comments_count !== undefined) {
                const shareId = commentElement?.closest('[data-share-id]')?.dataset.shareId;
                if (shareId) {
                    const countElement = document.getElementById(`share-comments-count-${shareId}`);
                    if (countElement) {
                        countElement.textContent = `${data.comments_count} comments`;
                    }
                }
            }
        } else {
            alert('Error deleting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting share comment:', error);
        alert('Error deleting comment. Please try again.');
    }
}

// Show share reply form
function showShareReplyForm(commentId) {
    const replyForm = document.getElementById(`share-reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.remove('hidden');
        replyForm.querySelector('textarea').focus();
    }
}

// Hide share reply form
function hideShareReplyForm(commentId) {
    const replyForm = document.getElementById(`share-reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.add('hidden');
        replyForm.querySelector('form').reset();
    }
}

// Toggle share comment dropdown
function toggleShareCommentDropdown(commentId) {
    const dropdown = document.getElementById(`share-comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

// Hide share comment dropdown
function hideShareCommentDropdown(commentId) {
    const dropdown = document.getElementById(`share-comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// Toggle modal share replies
function toggleModalShareReplies(commentId) {
    const repliesList = document.getElementById(`modal-share-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-share-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-share-replies-text-${commentId}`);

    if (repliesList && arrow && text) {
        const isHidden = repliesList.classList.contains('hidden');

        if (isHidden) {
            repliesList.classList.remove('hidden');
            arrow.style.transform = 'rotate(180deg)';
            text.textContent = 'Hide replies';
        } else {
            repliesList.classList.add('hidden');
            arrow.style.transform = 'rotate(0deg)';
            const replyCount = repliesList.children.length;
            text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
        }
    }
}

// Toggle share comment reply form
function toggleShareCommentReply(commentId) {
    const replyForm = document.getElementById(`share-reply-form-${commentId}`);
    if (replyForm) {
        if (replyForm.classList.contains('hidden')) {
            showShareReplyForm(commentId);
        } else {
            hideShareReplyForm(commentId);
        }
    }
}

// Cancel share comment reply
function cancelShareCommentReply(commentId) {
    hideShareReplyForm(commentId);
}





// Submit share comment reply
async function submitShareCommentReply(form) {
    const shareId = form.dataset.shareId;
    const parentId = form.dataset.parentId;
    const formData = new FormData(form);
    formData.append('parent_id', parentId);



    try {
        const response = await fetch(`/shares/${shareId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });

        const data = await response.json();

        if (data.success) {
            // Clear the form
            form.reset();

            // Hide the reply form
            hideShareReplyForm(parentId);

            // Update comments count
            const countElement = document.getElementById(`share-comments-count-${shareId}`);
            if (countElement) {
                countElement.textContent = `${data.comments_count} comments`;
            }

            // Add reply to the parent comment's replies section
            addShareReplyToDOM(parentId, data.comment);
        } else {
            alert('Error adding reply: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting share comment reply:', error);
        alert('Error adding reply. Please try again.');
    }
}

// Delete share
async function deleteShare(shareId) {
    if (!confirm('Are you sure you want to delete this share?')) {
        return;
    }

    try {
        const response = await fetch(`/shares/${shareId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove the shared post from DOM
            const shareElement = document.querySelector(`[data-share-id="${shareId}"]`);
            if (shareElement) {
                shareElement.remove();
            } else {
                // If no data-share-id, try to find the parent container
                const shareCard = document.querySelector(`#edit-share-form-${shareId}`);
                if (shareCard) {
                    const parentCard = shareCard.closest('.bg-white, .post-card, .share-card');
                    if (parentCard) {
                        parentCard.remove();
                    }
                }
            }
        } else {
            alert('Error deleting share: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting share:', error);
        alert('Error deleting share. Please try again.');
    }
}

// Add new share comment to DOM
function addShareCommentToDOM(shareId, comment) {
    const commentsList = document.getElementById(`share-comments-list-${shareId}`);
    if (!commentsList) return;

    // Remove "no comments" message if it exists
    const noComments = commentsList.querySelector('.no-comments');
    if (noComments) {
        noComments.remove();
    }

    // Create comment HTML
    const commentHTML = createShareCommentHTML(comment, shareId);

    // Add to the top of comments list
    commentsList.insertAdjacentHTML('afterbegin', commentHTML);
}

// Add new share reply to DOM
function addShareReplyToDOM(parentId, reply) {
    const parentComment = document.querySelector(`[data-comment-id="${parentId}"]`);
    if (!parentComment) return;

    // Find or create the nested-comments section
    let nestedComments = parentComment.querySelector('.nested-comments');
    if (!nestedComments) {
        // Create nested comments section if it doesn't exist
        const commentActions = parentComment.querySelector('.comment-actions');
        if (commentActions) {
            nestedComments = document.createElement('div');
            nestedComments.className = 'nested-comments ml-12 mt-3 space-y-3';
            commentActions.insertAdjacentElement('afterend', nestedComments);
        }
    }

    // Create reply HTML
    const replyHTML = createShareCommentHTML(reply, reply.commentable_id, true);

    // Add to the bottom of replies
    nestedComments.insertAdjacentHTML('beforeend', replyHTML);
}

// Create share comment HTML structure
function createShareCommentHTML(comment, shareId, isReply = false) {
    const timeAgo = formatTimeAgo(comment.created_at);
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;



    const marginClass = isReply ? 'ml-12' : '';

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-50/50 transition-colors duration-150 ${marginClass}" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-200 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-50/70 rounded-xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-gray-900 hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-500">${timeAgo}</span>
                            <div class="relative ml-auto">
                                <button onclick="toggleShareCommentDropdown(${comment.id})" class="text-gray-400 hover:text-gray-600 p-1 rounded-full hover:bg-gray-100">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
                                    </svg>
                                </button>
                                <div id="share-comment-dropdown-${comment.id}" class="hidden absolute right-0 top-8 w-36 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
                                    <button onclick="editShareComment(${comment.id}); hideShareCommentDropdown(${comment.id})" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>Edit</span>
                                    </button>
                                    <button onclick="deleteShareComment(${comment.id}); hideShareCommentDropdown(${comment.id})" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        <span>Delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="comment-content">
                            <p class="text-gray-800 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>

                        <!-- Edit form (hidden by default) -->
                        <div class="comment-edit-form hidden mt-3">
                            <form class="share-comment-edit-form" data-comment-id="${comment.id}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <input type="hidden" name="_method" value="PUT">
                                <textarea name="content" rows="2"
                                          class="w-full border border-gray-200 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">${comment.content}</textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="cancelEditShareComment(${comment.id})"
                                            class="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-800 rounded-md hover:bg-gray-100 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green transition-colors">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-3 text-xs">
                        <button onclick="toggleShareCommentLike(${comment.id})"
                                class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors group ${comment.is_liked_by_user ? 'text-red-600' : ''}"
                                id="share-comment-like-btn-${comment.id}">
                            <svg class="w-4 h-4 ${comment.is_liked_by_user ? 'text-red-600 fill-current' : ''} group-hover:scale-110 transition-transform duration-200"
                                 fill="${comment.is_liked_by_user ? 'currentColor' : 'none'}"
                                 stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <span id="share-comment-like-count-${comment.id}" class="font-medium">${comment.likes_count || 0}</span>
                        </button>
                        ${!isReply ? `
                            <button onclick="showShareReplyForm(${comment.id})"
                                    class="text-gray-500 hover:text-custom-green transition-colors duration-200">
                                Reply
                            </button>
                        ` : ''}
                    </div>

                    ${!isReply ? `
                        <!-- Reply Form (hidden by default) -->
                        <div class="reply-form hidden mt-3 ml-4" id="share-reply-form-${comment.id}">
                            <form class="share-comment-reply-form" data-parent-id="${comment.id}" data-share-id="${shareId}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <div class="flex space-x-3">
                                    <div class="flex-shrink-0">
                                        <img class="h-8 w-8 rounded-full ring-1 ring-gray-200 shadow-sm"
                                             src="${getCurrentUserAvatar()}"
                                             alt="Your avatar">
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="relative">
                                            <textarea name="content" rows="1"
                                                      placeholder="Write a reply..."
                                                      class="w-full px-3 py-2 border border-gray-200 rounded-full shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm bg-gray-50 hover:bg-white transition-colors duration-200"
                                                      required></textarea>
                                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                                                <button type="button" onclick="hideShareReplyForm(${comment.id})"
                                                        class="p-1.5 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                                    </svg>
                                                </button>
                                                <button type="submit"
                                                        class="p-1.5 bg-custom-green text-white rounded-full hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green shadow-sm transition-all duration-200 hover:scale-105">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Helper function to get current user ID
function getCurrentUserId() {
    const userIdMeta = document.querySelector('meta[name="user-id"]');
    return userIdMeta ? userIdMeta.getAttribute('content') : null;
}

// Helper function to check if current user is admin
function isAdmin() {
    const userRoleMeta = document.querySelector('meta[name="user-role"]');
    return userRoleMeta ? userRoleMeta.getAttribute('content') === 'admin' : false;
}
