<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['group' => null, 'organization' => null]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['group' => null, 'organization' => null]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<!-- Post Creation Modal -->
<div id="postCreationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900">
                    <?php if($group): ?>
                        Create Post in <?php echo e($group->name); ?>

                    <?php elseif($organization): ?>
                        Create Announcement for <?php echo e($organization->name); ?>

                    <?php else: ?>
                        Create Post
                    <?php endif; ?>
                </h3>
                <button onclick="closePostModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Modal Body -->
        <form action="<?php echo e(route('posts.store')); ?>" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
            <?php echo csrf_field(); ?>
            
            <?php if($group): ?>
                <input type="hidden" name="group_id" value="<?php echo e($group->id); ?>">
            <?php elseif($organization): ?>
                <input type="hidden" name="organization_id" value="<?php echo e($organization->id); ?>">
            <?php endif; ?>

            <!-- User Info -->
            <div class="flex items-center space-x-3">
                <img class="h-10 w-10 rounded-full" src="<?php echo e(auth()->user()->avatar ? Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF'); ?>" alt="<?php echo e(auth()->user()->name); ?>">
                <div>
                    <p class="text-sm font-medium text-gray-900"><?php echo e(auth()->user()->name); ?></p>
                    <p class="text-xs text-gray-500">
                        <?php if($group): ?>
                            Posting to <?php echo e($group->name); ?>

                            <?php if($group->post_approval === 'required'): ?>
                                <span class="text-yellow-600">(Requires approval)</span>
                            <?php endif; ?>
                        <?php elseif($organization): ?>
                            Official announcement for <?php echo e($organization->name); ?>

                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <!-- Post Type -->
            <div>
                <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Post Type</label>
                <select name="type" id="type" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    <option value="general">General</option>
                    <option value="announcement">Announcement</option>
                    <option value="event">Event</option>
                    <?php if($organization): ?>
                        <option value="financial_report">Financial Report</option>
                    <?php endif; ?>
                </select>
            </div>

            <!-- Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Title *</label>
                <input type="text" name="title" id="title" required
                       class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                       placeholder="What's the title of your post?">
            </div>

            <!-- Content -->
            <div>
                <label for="content" class="block text-sm font-medium text-gray-700 mb-1">Content *</label>
                <textarea name="content" id="content" rows="4" required
                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Share your thoughts, updates, or information..."></textarea>
            </div>

            <!-- Image Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Images</label>
                <?php if (isset($component)) { $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload-zone','data' => ['name' => 'images','accept' => 'image/*','multiple' => true,'maxSize' => '10MB','allowedTypes' => ['jpg', 'jpeg', 'png', 'gif'],'title' => 'Upload Images','description' => 'Drag and drop images here or click to browse']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload-zone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'images','accept' => 'image/*','multiple' => true,'max-size' => '10MB','allowed-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['jpg', 'jpeg', 'png', 'gif']),'title' => 'Upload Images','description' => 'Drag and drop images here or click to browse']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $attributes = $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $component = $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
            </div>

            <!-- File Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">File Attachments</label>
                <?php if($group && $group->allow_file_sharing): ?>
                    <!-- Group post attachments with group-specific settings -->
                    <?php if (isset($component)) { $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload-zone','data' => ['name' => 'attachments','accept' => ''.e($group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*').'','multiple' => true,'maxSize' => ''.e($group->max_file_size_mb).'MB','allowedTypes' => $group->allowed_file_types,'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload-zone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'attachments','accept' => ''.e($group->allowed_file_types ? '.' . implode(',.', $group->allowed_file_types) : '*').'','multiple' => true,'max-size' => ''.e($group->max_file_size_mb).'MB','allowed-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($group->allowed_file_types),'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $attributes = $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $component = $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
                <?php elseif($group && !$group->allow_file_sharing): ?>
                    <!-- Group doesn't allow file sharing -->
                    <div class="text-sm text-gray-500 italic">
                        File attachments are not allowed in this group.
                    </div>
                <?php else: ?>
                    <!-- Normal post attachments with default settings -->
                    <?php if (isset($component)) { $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload-zone','data' => ['name' => 'attachments','accept' => '.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z','multiple' => true,'maxSize' => '10MB','allowedTypes' => ['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z'],'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload-zone'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'attachments','accept' => '.pdf,.doc,.docx,.txt,.rtf,.jpg,.jpeg,.png,.gif,.webp,.mp4,.avi,.mov,.zip,.rar,.7z','multiple' => true,'max-size' => '10MB','allowed-types' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['pdf', 'doc', 'docx', 'txt', 'rtf', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'mp4', 'avi', 'mov', 'zip', 'rar', '7z']),'title' => 'Upload Files','description' => 'Drag and drop files here or click to browse (PDF, DOC, images, videos, archives - max 10MB)']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $attributes = $__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__attributesOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f)): ?>
<?php $component = $__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f; ?>
<?php unset($__componentOriginal65f6e33a0df551bea8e4f9fb1f9d804f); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Pin Post (for moderators/admins) -->
            <?php if(($group && $group->userCanModerate(auth()->user())) || ($organization && $organization->userCanPost(auth()->user()))): ?>
                <div class="flex items-center">
                    <input type="checkbox" name="is_pinned" id="is_pinned" value="1" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_pinned" class="ml-2 text-sm text-gray-900">
                        Pin this post to the top
                    </label>
                </div>
            <?php endif; ?>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                <button type="button" onclick="closePostModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400">
                    Cancel
                </button>
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700">
                    <?php if($group && $group->post_approval === 'required'): ?>
                        Submit for Approval
                    <?php else: ?>
                        Post
                    <?php endif; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    function openCreatePostModal() {
        document.getElementById('postCreationModal').classList.remove('hidden');
        document.getElementById('postCreationModal').classList.add('flex');
        document.body.style.overflow = 'hidden';
    }

    function closePostModal() {
        document.getElementById('postCreationModal').classList.add('hidden');
        document.getElementById('postCreationModal').classList.remove('flex');
        document.body.style.overflow = 'auto';

        // Reset form
        document.querySelector('#postCreationModal form').reset();

        // Reset file upload zones
        const imagePreviews = document.querySelectorAll('#postCreationModal [id$="-preview"]');
        imagePreviews.forEach(preview => {
            preview.classList.add('hidden');
            preview.innerHTML = '';
        });
    }
</script>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\components\post-creation-modal.blade.php ENDPATH**/ ?>