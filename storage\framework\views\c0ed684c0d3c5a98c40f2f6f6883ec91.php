<?php if (isset($component)) { $__componentOriginal4969f54a92451522b65593c595a4fb0c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4969f54a92451522b65593c595a4fb0c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.unilink-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('unilink-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="max-w-6xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">My Followers</h1>
                    <p class="text-gray-600 mt-1"><?php echo e(number_format($followers->total())); ?> people follow you</p>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="<?php echo e(route('follow-management.following')); ?>" 
                       class="text-blue-600 hover:text-blue-800 font-medium transition-colors">
                        View Following
                    </a>
                    <a href="<?php echo e(route('profile.show')); ?>" 
                       class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                        Back to Profile
                    </a>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <form method="GET" action="<?php echo e(route('follow-management.followers')); ?>" class="flex items-center space-x-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" 
                               name="search" 
                               value="<?php echo e($search); ?>"
                               placeholder="Search followers by name or email..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                    </div>
                </div>
                <button type="submit" 
                        class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Search
                </button>
                <?php if($search): ?>
                    <a href="<?php echo e(route('follow-management.followers')); ?>" 
                       class="text-gray-500 hover:text-gray-700 px-3 py-2 transition-colors">
                        Clear
                    </a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Followers List -->
        <div class="bg-white rounded-lg shadow-sm">
            <?php if($followers->count() > 0): ?>
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $followers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $follower): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-6 flex items-center justify-between hover:bg-gray-50 transition-colors">
                            <div class="flex items-center space-x-4">
                                <img src="<?php echo e($follower->avatar ? asset('storage/' . $follower->avatar) : asset('images/default-avatar.png')); ?>" 
                                     alt="<?php echo e($follower->name); ?>" 
                                     class="w-16 h-16 rounded-full object-cover">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <a href="<?php echo e(route('profile.user', $follower)); ?>" 
                                           class="hover:text-blue-600 transition-colors">
                                            <?php echo e($follower->name); ?>

                                        </a>
                                    </h3>
                                    <p class="text-gray-600"><?php echo e($follower->email); ?></p>
                                    <?php if($follower->bio): ?>
                                        <p class="text-sm text-gray-500 mt-1"><?php echo e(Str::limit($follower->bio, 100)); ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span><?php echo e($follower->followers()->count()); ?> followers</span>
                                        <span><?php echo e($follower->following()->count()); ?> following</span>
                                        <span>Followed you <?php echo e($follower->pivot->created_at->diffForHumans()); ?></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <?php if(auth()->id() !== $follower->id): ?>
                                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('user-follower', ['user' => $follower]);

$__html = app('livewire')->mount($__name, $__params, 'follower-' . $follower->id, $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                <?php endif; ?>
                                <a href="<?php echo e(route('profile.user', $follower)); ?>" 
                                   class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                                    View Profile
                                </a>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    <?php echo e($followers->links()); ?>

                </div>
            <?php else: ?>
                <div class="p-12 text-center">
                    <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">
                        <?php if($search): ?>
                            No followers found
                        <?php else: ?>
                            No followers yet
                        <?php endif; ?>
                    </h3>
                    <p class="text-gray-600">
                        <?php if($search): ?>
                            Try adjusting your search terms.
                        <?php else: ?>
                            When people follow you, they'll appear here.
                        <?php endif; ?>
                    </p>
                    <?php if($search): ?>
                        <div class="mt-4">
                            <a href="<?php echo e(route('follow-management.followers')); ?>" 
                               class="text-blue-600 hover:text-blue-800 font-medium">
                                View all followers
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $attributes = $__attributesOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__attributesOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4969f54a92451522b65593c595a4fb0c)): ?>
<?php $component = $__componentOriginal4969f54a92451522b65593c595a4fb0c; ?>
<?php unset($__componentOriginal4969f54a92451522b65593c595a4fb0c); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\follow-management\followers.blade.php ENDPATH**/ ?>