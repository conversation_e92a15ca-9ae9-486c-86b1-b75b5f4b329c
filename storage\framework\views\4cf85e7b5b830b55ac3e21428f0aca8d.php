<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
        <?php if(auth()->guard()->check()): ?>
        <meta name="user-id" content="<?php echo e(auth()->id()); ?>">
        <meta name="user-role" content="<?php echo e(auth()->user()->role); ?>">
        <?php endif; ?>

        <title><?php echo e(config('app.name', 'Laravel')); ?></title>

        <!-- Preload critical resources -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link rel="dns-prefetch" href="https://fonts.bunny.net">

        <!-- Fonts with display=swap to prevent layout shift -->
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Critical CSS and JS -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Defer non-critical JavaScript to prevent layout blocking -->
        <script defer src="<?php echo e(asset('js/reactions.js')); ?>"></script>
    </head>
    <body class="font-sans antialiased">
        <div class="min-h-screen bg-gray-100">
            <?php echo $__env->make('layouts.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

            <!-- Page Heading -->
            <?php if(isset($header)): ?>
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        <?php echo e($header); ?>

                    </div>
                </header>
            <?php endif; ?>

            <!-- Page Content -->
            <main>
                <?php echo e($slot); ?>

            </main>
        </div>

        <!-- Ensure DOM is ready before layout-dependent operations -->
        <script>
            // Prevent layout thrashing by ensuring DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
                // Force a reflow to ensure all styles are applied
                document.body.offsetHeight;

                // Initialize any layout-dependent functionality here
                if (window.initializeLayoutDependentFeatures) {
                    window.initializeLayoutDependentFeatures();
                }
            });
        </script>
    </body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views\layouts\app.blade.php ENDPATH**/ ?>