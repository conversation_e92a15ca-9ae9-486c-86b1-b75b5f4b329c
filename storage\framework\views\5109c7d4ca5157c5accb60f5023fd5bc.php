<?php
    use App\Models\Reaction;

    // Get user reaction and counts
    $userReaction = auth()->check() ? $share->reactions()->where('user_id', auth()->id())->first() : null;
    $reactionCounts = $share->reactions()
        ->selectRaw('type, COUNT(*) as count')
        ->groupBy('type')
        ->pluck('count', 'type')
        ->toArray();

    // Generate reaction button HTML similar to FacebookReactionSystem
    $buttonColor = 'text-gray-500';
    $buttonIcon = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>';
    $buttonText = 'Like';

    if ($userReaction) {
        $details = Reaction::getReactionDetails($userReaction->type);
        $buttonColor = $details['color'];
        $buttonIcon = '<img src="' . $details['emoji'] . '" alt="' . $details['label'] . '" class="w-5 h-5 reaction-emoji" onerror="this.style.display=\'none\';">';
        $buttonText = $details['label'];
    }

    $totalReactions = array_sum($reactionCounts);
    $reactionCountHTML = '';
    if ($totalReactions > 0) {
        $reactionCountHTML = '<span class="text-sm text-gray-600 ml-2">' . $totalReactions . '</span>';
    }
?>

<div class="reaction-wrapper relative inline-block">
    <button class="reaction-btn flex items-center space-x-2 <?php echo e($buttonColor); ?> transition-colors duration-200 py-2 px-3 rounded-lg hover:bg-gray-100"
            data-target-id="<?php echo e($share->id); ?>"
            data-target-type="share"
            data-current-reaction="<?php echo e($userReaction ? $userReaction->type : ''); ?>">
        <?php echo $buttonIcon; ?>

        <span class="font-medium"><?php echo e($buttonText); ?></span>
    </button>
    <?php echo $reactionCountHTML; ?>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Ensure the reaction system is initialized for shared posts
    if (window.facebookReactionSystem) {
        window.facebookReactionSystem.createReactionPopups();
    }
});
</script>
</div>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/livewire/shared-post-reactions.blade.php ENDPATH**/ ?>